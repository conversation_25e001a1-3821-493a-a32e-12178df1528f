#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取Git仓库根目录
const gitRoot = execSync('git rev-parse --show-toplevel', { encoding: 'utf-8' }).trim();
const hooksDir = path.join(gitRoot, '.git', 'hooks');

// 确保hooks目录存在
if (!fs.existsSync(hooksDir)) {
  fs.mkdirSync(hooksDir, { recursive: true });
}

// 计算脚本的绝对路径
const aiCodeStatsPath = path.resolve(__dirname, 'ai-code-stats.js');
const cleanupMarkersPath = path.resolve(__dirname, 'cleanup-markers.js');

// 创建pre-push钩子
const hookContent = `#!/bin/bash
# AI代码统计钩子 - 在push时触发

# 日志文件路径
LOG_FILE="\$(git rev-parse --show-toplevel)/.git/hooks/ai-stats.log"

# 函数：记录日志
log_message() {
  echo "\$(date '+%Y-%m-%d %H:%M:%S') - \$1" >> "\$LOG_FILE"
}

# 函数：查找 Node.js 可执行文件
find_node() {
  # 常见的 Node.js 路径
  local node_paths=(
    "/usr/local/bin/node"
    "/usr/bin/node"
    "/opt/homebrew/bin/node"
    "\$(which node 2>/dev/null)"
    "\$HOME/.nvm/current/bin/node"
    "\$HOME/.volta/bin/node"
  )

  for node_path in "\${node_paths[@]}"; do
    if [ -x "\$node_path" ]; then
      echo "\$node_path"
      return 0
    fi
  done

  # 如果都找不到，尝试从 PATH 中查找
  if command -v node >/dev/null 2>&1; then
    echo "node"
    return 0
  fi

  return 1
}

# 查找 Node.js
NODE_CMD=\$(find_node)
if [ \$? -ne 0 ]; then
  echo "错误: 找不到 Node.js 可执行文件" >&2
  log_message "错误: 找不到 Node.js 可执行文件"
  exit 1
fi

log_message "使用 Node.js: \$NODE_CMD"

# 脚本路径
AI_STATS_SCRIPT="${aiCodeStatsPath}"
CLEANUP_SCRIPT="${cleanupMarkersPath}"

# 检查脚本文件是否存在
if [ ! -f "\$AI_STATS_SCRIPT" ]; then
  echo "错误: AI统计脚本不存在: \$AI_STATS_SCRIPT" >&2
  log_message "错误: AI统计脚本不存在: \$AI_STATS_SCRIPT"
  exit 1
fi

# 函数：执行AI代码统计
perform_ai_stats() {
  local commit_range="\$1"
  local commit_count="\$2"

  log_message "开始执行AI代码统计，提交数量: \$commit_count"

  if [ "\$commit_count" -eq 1 ]; then
    # 单个提交，使用详细模式
    echo "正在统计提交: \$commit_range"
    log_message "统计单个提交: \$commit_range"
    "\$NODE_CMD" "\$AI_STATS_SCRIPT" "\$commit_range" "detailed" "no-cleanup" 2>&1 | tee -a "\$LOG_FILE"
    local exit_code=\${PIPESTATUS[0]}
    if [ \$exit_code -ne 0 ]; then
      log_message "AI统计执行失败，退出码: \$exit_code"
      return \$exit_code
    fi
  else
    # 多个提交，使用汇总模式
    echo "检测到 \$commit_count 个新提交，开始统计..."
    echo ""
    log_message "统计多个提交，数量: \$commit_count"

    # 对每个提交使用简洁模式
    for COMMIT in \$commit_range; do
      log_message "统计提交: \$COMMIT"
      "\$NODE_CMD" "\$AI_STATS_SCRIPT" "\$COMMIT" "summary" "no-cleanup" 2>&1 | tee -a "\$LOG_FILE"
      local exit_code=\${PIPESTATUS[0]}
      if [ \$exit_code -ne 0 ]; then
        log_message "提交 \$COMMIT 统计失败，退出码: \$exit_code"
        return \$exit_code
      fi
    done

    echo ""
    echo "===== 本次推送汇总统计 ====="

    # 计算总体统计
    TOTAL_ADDED=0
    TOTAL_DELETED=0
    TOTAL_FILES=0

    for COMMIT in $commit_range; do
      STATS=$(git diff --numstat $COMMIT^..$COMMIT 2>/dev/null || git show --numstat $COMMIT 2>/dev/null)
      if [ ! -z "$STATS" ]; then
        COMMIT_ADDED=$(echo "$STATS" | awk '{sum+=$1} END {print sum+0}')
        COMMIT_DELETED=$(echo "$STATS" | awk '{sum+=$2} END {print sum+0}')
        COMMIT_FILES=$(echo "$STATS" | wc -l)

        TOTAL_ADDED=$((TOTAL_ADDED + COMMIT_ADDED))
        TOTAL_DELETED=$((TOTAL_DELETED + COMMIT_DELETED))
        TOTAL_FILES=$((TOTAL_FILES + COMMIT_FILES))
      fi
    done

    echo "总计 $commit_count 个提交"
    echo "代码变更: +$TOTAL_ADDED/-$TOTAL_DELETED ($TOTAL_FILES 个文件)"
    echo "时间范围: $(git show -s --format='%ci' $(echo "$commit_range" | tail -1)) 到 $(git show -s --format='%ci' $(echo "$commit_range" | head -1))"
  fi
}

# 函数：清理AI标记并创建提交
cleanup_and_commit() {
  echo ""
  echo "===== 清理AI标记 ====="
  log_message "开始清理AI标记"

  # 检查清理脚本是否存在
  if [ ! -f "\$CLEANUP_SCRIPT" ]; then
    echo "警告: 清理脚本不存在: \$CLEANUP_SCRIPT" >&2
    log_message "警告: 清理脚本不存在: \$CLEANUP_SCRIPT"
    return 0
  fi

  # 执行清理
  CLEANUP_OUTPUT=\$("\$NODE_CMD" "\$CLEANUP_SCRIPT" --quiet 2>&1)
  CLEANUP_EXIT_CODE=\$?
  log_message "清理脚本执行完成，退出码: \$CLEANUP_EXIT_CODE"

  if [ \$CLEANUP_EXIT_CODE -eq 0 ]; then
    # 检查是否有文件被修改
    if git diff --quiet; then
      echo "没有发现需要清理的AI标记"
      log_message "没有发现需要清理的AI标记"
      return 0
    else
      echo "发现AI标记，正在清理..."
      echo "\$CLEANUP_OUTPUT"
      log_message "发现AI标记，正在清理"

      # 添加所有修改的文件
      git add -u

      # 创建清理提交
      git commit -m "清理AI代码标记

自动清理提交，移除开发过程中的AI代码标记。
- 保持代码功能不变
- 由 ai-code-stats 工具自动生成"

      echo "✅ 已提交清理后的代码"
      log_message "已提交清理后的代码"
      return 0
    fi
  else
    echo "⚠️ 清理过程中出现错误: \$CLEANUP_OUTPUT"
    log_message "清理过程中出现错误: \$CLEANUP_OUTPUT"
    return 1
  fi
}

# 主要逻辑：读取 pre-push hook 的标准输入
# 格式: <local ref> <local sha1> <remote ref> <remote sha1>
PROCESSED_ANY=false

log_message "开始处理 pre-push hook"

while read local_ref local_sha remote_ref remote_sha; do
  log_message "处理推送: \$local_ref \$local_sha \$remote_ref \$remote_sha"

  # 跳过删除操作（local_sha 为全零）
  if [ "\$local_sha" = "0000000000000000000000000000000000000000" ]; then
    log_message "跳过删除操作"
    continue
  fi

  PROCESSED_ANY=true

  # 如果远程分支不存在（remote_sha 为全零），这是新分支
  if [ "\$remote_sha" = "0000000000000000000000000000000000000000" ]; then
    echo "检测到新分支推送: \$local_ref"
    log_message "新分支推送: \$local_ref"
    # 对于新分支，只统计当前提交（HEAD）
    perform_ai_stats "\$local_sha" 1
    if [ \$? -ne 0 ]; then
      log_message "新分支统计失败"
      exit 1
    fi
  else
    # 对于现有分支，统计本地与远程的差异提交
    echo "检测到分支更新: \$local_ref"
    log_message "分支更新: \$local_ref"
    COMMIT_RANGE=\$(git rev-list \$remote_sha..\$local_sha)

    if [ -z "\$COMMIT_RANGE" ]; then
      echo "没有新的提交需要统计"
      log_message "没有新的提交需要统计"
      continue
    fi

    # 计算提交数量
    COMMIT_COUNT=\$(echo "\$COMMIT_RANGE" | wc -l)
    log_message "发现 \$COMMIT_COUNT 个新提交"

    # 执行统计
    perform_ai_stats "\$COMMIT_RANGE" "\$COMMIT_COUNT"
    if [ \$? -ne 0 ]; then
      log_message "分支更新统计失败"
      exit 1
    fi
  fi
done

# 如果处理了任何推送，执行清理
if [ "\$PROCESSED_ANY" = true ]; then
  log_message "开始执行清理流程"
  cleanup_and_commit
  if [ \$? -ne 0 ]; then
    log_message "清理流程失败"
    exit 1
  fi
fi

# 如果没有从标准输入读取到任何内容，使用备用方案
if [ ! -t 0 ]; then
  # 标准输入不是终端，说明是通过 git push 调用的
  log_message "通过 git push 调用，正常退出"
  exit 0
else
  # 手动调用的情况，统计当前分支与远程的差异
  echo "手动执行模式 - 统计当前分支的未推送提交"
  log_message "手动执行模式"
  BRANCH_NAME=\$(git symbolic-ref --short HEAD)
  REMOTE_REF=\$(git for-each-ref --format='%(upstream:short)' refs/heads/\$BRANCH_NAME)

  if [ -z "\$REMOTE_REF" ]; then
    echo "当前分支没有设置上游分支，统计最新提交"
    echo "正在统计提交: HEAD"
    log_message "统计最新提交: HEAD"
    "\$NODE_CMD" "\$AI_STATS_SCRIPT" "HEAD" 2>&1 | tee -a "\$LOG_FILE"
    if [ \${PIPESTATUS[0]} -ne 0 ]; then
      log_message "手动模式统计失败"
      exit 1
    fi
  else
    COMMIT_RANGE=\$(git rev-list \$REMOTE_REF..HEAD)
    if [ -z "\$COMMIT_RANGE" ]; then
      echo "没有新的提交需要统计"
      log_message "没有新的提交需要统计"
    else
      for COMMIT in \$COMMIT_RANGE; do
        echo "正在统计提交: \$COMMIT"
        log_message "统计提交: \$COMMIT"
        "\$NODE_CMD" "\$AI_STATS_SCRIPT" "\$COMMIT" 2>&1 | tee -a "\$LOG_FILE"
        if [ \${PIPESTATUS[0]} -ne 0 ]; then
          log_message "提交 \$COMMIT 统计失败"
          exit 1
        fi
      done
    fi
  fi
fi

log_message "AI代码统计钩子执行完成"
`;

const hookPath = path.join(hooksDir, 'pre-push');
fs.writeFileSync(hookPath, hookContent);
fs.chmodSync(hookPath, '755');

console.log(`Git pre-push钩子已安装到: ${hookPath}`);
console.log('现在每次推送代码时都会自动统计AI生成的代码行数');
